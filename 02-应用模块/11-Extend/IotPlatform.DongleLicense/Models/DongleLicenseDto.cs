using System.ComponentModel.DataAnnotations;

namespace IotPlatform.DongleLicense.Models;

/// <summary>
/// 初始化授权输入模型
/// </summary>
public class InitializeAuthInput
{
    /// <summary>
    /// 硬件ID（用户传入的授权数据）
    /// </summary>
    [Required(ErrorMessage = "硬件ID不能为空")]
    [StringLength(100, ErrorMessage = "硬件ID长度不能超过100个字符")]
    public string HardwareId { get; set; } = string.Empty;
}

/// <summary>
/// 加密锁服务状态输出
/// </summary>
public class DongleServiceStatusOutput
{
    /// <summary>
    /// 服务是否运行中
    /// </summary>
    public bool IsRunning { get; set; }

    /// <summary>
    /// 配置是否启用
    /// </summary>
    public bool ConfigEnabled { get; set; }

    /// <summary>
    /// 检查间隔（分钟）
    /// </summary>
    public int CheckIntervalMinutes { get; set; }

    /// <summary>
    /// 最后检查时间
    /// </summary>
    public DateTime? LastCheckTime { get; set; }

    /// <summary>
    /// 最后检查是否成功
    /// </summary>
    public bool? LastCheckSuccess { get; set; }

    /// <summary>
    /// 最后检查错误信息
    /// </summary>
    public string? LastCheckError { get; set; }

    /// <summary>
    /// 最后检查设备数量
    /// </summary>
    public int LastCheckDeviceCount { get; set; }
}

/// <summary>
/// 加密锁设备信息输出
/// </summary>
public class DongleDeviceOutput
{
    /// <summary>
    /// 设备索引
    /// </summary>
    public int Index { get; set; }

    /// <summary>
    /// COS版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 产品类型描述
    /// </summary>
    public string ProductType { get; set; } = string.Empty;

    /// <summary>
    /// 出厂日期
    /// </summary>
    public string BirthDay { get; set; } = string.Empty;

    /// <summary>
    /// 代理商编号
    /// </summary>
    public string AgentId { get; set; } = string.Empty;

    /// <summary>
    /// 产品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 用户ID
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 硬件ID
    /// </summary>
    public string HardwareId { get; set; } = string.Empty;

    /// <summary>
    /// 是否为母锁
    /// </summary>
    public bool IsMother { get; set; }

    /// <summary>
    /// 设备类型
    /// </summary>
    public string DeviceType { get; set; } = string.Empty;

    /// <summary>
    /// 是否可以正常打开和关闭
    /// </summary>
    public bool CanOpenClose { get; set; }

    /// <summary>
    /// 打开关闭测试的错误信息
    /// </summary>
    public string? OpenCloseError { get; set; }

    /// <summary>
    /// 状态描述
    /// </summary>
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// 加密锁设备列表输出
/// </summary>
public class DongleDeviceListOutput
{
    /// <summary>
    /// 检查时间
    /// </summary>
    public DateTime CheckTime { get; set; }

    /// <summary>
    /// 检查是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 设备数量
    /// </summary>
    public int DeviceCount { get; set; }

    /// <summary>
    /// 检查耗时（毫秒）
    /// </summary>
    public long ElapsedMs { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 设备列表
    /// </summary>
    public List<DongleDeviceOutput> Devices { get; set; } = new();
}

/// <summary>
/// 加密锁健康状态输出
/// </summary>
public class DongleHealthOutput
{
    /// <summary>
    /// 服务状态
    /// </summary>
    public ServiceStatus Service { get; set; } = new();

    /// <summary>
    /// 最后检查状态
    /// </summary>
    public LastCheckStatus? LastCheck { get; set; }

    /// <summary>
    /// 整体状态
    /// </summary>
    public OverallStatus Overall { get; set; } = new();

    /// <summary>
    /// 服务状态
    /// </summary>
    public class ServiceStatus
    {
        /// <summary>
        /// 是否运行中
        /// </summary>
        public bool IsRunning { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// 最后检查状态
    /// </summary>
    public class LastCheckStatus
    {
        /// <summary>
        /// 检查时间
        /// </summary>
        public DateTime Time { get; set; }

        /// <summary>
        /// 检查是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 设备数量
        /// </summary>
        public int DeviceCount { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? Message { get; set; }
    }

    /// <summary>
    /// 整体状态
    /// </summary>
    public class OverallStatus
    {
        /// <summary>
        /// 状态描述
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
}
