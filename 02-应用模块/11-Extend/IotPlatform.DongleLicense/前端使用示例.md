# 前端使用示例

## JavaScript/TypeScript 示例

### 1. 检查授权状态并显示相应界面

```javascript
// 检查是否已有授权密钥
async function checkAuthStatus() {
    try {
        const response = await fetch('/dongleLicense/hasAuthKey');
        const result = await response.json();
        
        if (result.hasAuthKey) {
            // 已有授权密钥，显示正常界面
            showMainInterface();
            // 可以进一步检查授权验证状态
            checkAuthValidation();
        } else {
            // 未初始化授权，显示初始化界面
            showInitializationInterface();
        }
    } catch (error) {
        console.error('检查授权状态失败:', error);
        showErrorInterface('无法连接到授权服务');
    }
}

// 检查授权验证状态
async function checkAuthValidation() {
    try {
        const response = await fetch('/dongleLicense/authStatus');
        const result = await response.json();
        
        if (result.lastAuthCheck && result.lastAuthCheck.isAuthorized) {
            showAuthStatus('success', '授权验证通过');
        } else {
            showAuthStatus('error', result.lastAuthCheck?.authStatus || '授权验证失败');
        }
    } catch (error) {
        console.error('检查授权验证失败:', error);
        showAuthStatus('error', '无法获取授权状态');
    }
}
```

### 2. 初始化授权密钥

```javascript
// 初始化授权密钥
async function initializeAuth() {
    const hardwareIdInput = document.getElementById('hardwareIdInput');
    const initButton = document.getElementById('initAuthButton');
    const statusDiv = document.getElementById('authStatus');

    // 获取用户输入的硬件ID
    const hardwareId = hardwareIdInput.value.trim();
    if (!hardwareId) {
        statusDiv.innerHTML = `
            <div class="error">
                <h3>输入错误</h3>
                <p>请输入硬件ID</p>
            </div>
        `;
        return;
    }

    // 禁用按钮，显示加载状态
    initButton.disabled = true;
    initButton.textContent = '正在初始化...';
    statusDiv.textContent = '正在保存授权信息...';

    try {
        const response = await fetch('/dongleLicense/initializeAuth', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                hardwareId: hardwareId
            })
        });

        const result = await response.json();

        if (result.success) {
            statusDiv.innerHTML = `
                <div class="success">
                    <h3>授权初始化成功！</h3>
                    <p>硬件ID: ${result.hardwareId}</p>
                    <p>时间: ${new Date(result.timestamp).toLocaleString()}</p>
                </div>
            `;

            // 3秒后刷新页面或切换到主界面
            setTimeout(() => {
                showMainInterface();
            }, 3000);
        } else {
            statusDiv.innerHTML = `
                <div class="error">
                    <h3>初始化失败</h3>
                    <p>${result.message}</p>
                </div>
            `;
            initButton.disabled = false;
            initButton.textContent = '重新初始化';
        }
    } catch (error) {
        console.error('初始化授权失败:', error);
        statusDiv.innerHTML = `
            <div class="error">
                <h3>初始化异常</h3>
                <p>请检查网络连接</p>
            </div>
        `;
        initButton.disabled = false;
        initButton.textContent = '重新初始化';
    }
}
```

### 3. 实时监控授权状态

```javascript
// 定期检查系统健康状态
function startHealthMonitoring() {
    setInterval(async () => {
        try {
            const response = await fetch('/dongleLicense/health');
            const result = await response.json();
            
            updateHealthStatus(result);
        } catch (error) {
            console.error('健康检查失败:', error);
            updateHealthStatus({ overall: { status: '连接异常' } });
        }
    }, 30000); // 每30秒检查一次
}

function updateHealthStatus(healthData) {
    const statusElement = document.getElementById('systemStatus');
    const overallStatus = healthData.overall?.status || '未知';
    
    let statusClass = 'status-unknown';
    let statusText = overallStatus;
    
    switch (overallStatus) {
        case '健康':
            statusClass = 'status-healthy';
            statusText = '系统正常';
            break;
        case '异常':
            statusClass = 'status-error';
            statusText = '系统异常';
            break;
        default:
            statusClass = 'status-warning';
            statusText = '状态未知';
    }
    
    statusElement.className = statusClass;
    statusElement.textContent = statusText;
    
    // 更新详细信息
    if (healthData.lastCheck) {
        const authStatus = healthData.lastCheck.authStatus || '未知';
        document.getElementById('authStatusDetail').textContent = authStatus;
    }
}
```

## HTML 界面示例

### 初始化界面

```html
<div id="initializationInterface" class="interface">
    <div class="card">
        <h2>系统授权初始化</h2>
        <p>请输入硬件ID来初始化系统授权密钥。</p>

        <div class="form-group">
            <label for="hardwareIdInput">硬件ID：</label>
            <input type="text"
                   id="hardwareIdInput"
                   placeholder="请输入硬件ID，例如：01 02 03 04 05 06 07 08"
                   maxlength="100"
                   class="hardware-id-input">
            <small class="help-text">硬件ID将用于生成授权密钥，请确保输入正确</small>
        </div>

        <div class="warning">
            <strong>注意：</strong>此操作只能执行一次，请确保硬件ID输入正确！
        </div>

        <button id="initAuthButton" onclick="initializeAuth()">
            初始化授权密钥
        </button>

        <div id="authStatus" class="status-message"></div>
    </div>
</div>
```

### 主界面

```html
<div id="mainInterface" class="interface">
    <div class="header">
        <h1>IoT平台管理系统</h1>
        <div class="status-bar">
            <span>系统状态: </span>
            <span id="systemStatus" class="status-indicator">检查中...</span>
            <span>授权状态: </span>
            <span id="authStatusDetail" class="auth-status">检查中...</span>
        </div>
    </div>
    
    <div class="main-content">
        <!-- 主要功能界面 -->
        <p>系统已授权，可以正常使用。</p>
    </div>
</div>
```

## CSS 样式示例

```css
.interface {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.card {
    background: #fff;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
    color: #856404;
}

.form-group {
    margin: 20px 0;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
}

.hardware-id-input {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    font-family: monospace;
    box-sizing: border-box;
}

.hardware-id-input:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.help-text {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 14px;
}

button {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    margin: 20px 0;
}

button:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 15px;
    border-radius: 4px;
    margin: 20px 0;
}

.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 4px;
    margin: 20px 0;
}

.status-healthy {
    color: #28a745;
    font-weight: bold;
}

.status-error {
    color: #dc3545;
    font-weight: bold;
}

.status-warning {
    color: #ffc107;
    font-weight: bold;
}

.status-bar {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}
```

## Vue.js 组件示例

```vue
<template>
  <div class="auth-manager">
    <!-- 初始化界面 -->
    <div v-if="!hasAuthKey" class="init-interface">
      <h2>系统授权初始化</h2>
      <p>请输入硬件ID来初始化系统授权密钥</p>

      <div class="form-group">
        <label for="hardwareId">硬件ID：</label>
        <input
          v-model="hardwareId"
          type="text"
          id="hardwareId"
          placeholder="请输入硬件ID，例如：01 02 03 04 05 06 07 08"
          maxlength="100"
          class="hardware-id-input"
        >
        <small class="help-text">硬件ID将用于生成授权密钥，请确保输入正确</small>
      </div>

      <button
        @click="initializeAuth"
        :disabled="isInitializing || !hardwareId.trim()"
        class="init-button"
      >
        {{ isInitializing ? '正在初始化...' : '初始化授权密钥' }}
      </button>
      <div v-if="initMessage" :class="initMessageClass">
        {{ initMessage }}
      </div>
    </div>
    
    <!-- 主界面 -->
    <div v-else class="main-interface">
      <div class="status-bar">
        <span>授权状态: </span>
        <span :class="authStatusClass">{{ authStatusText }}</span>
      </div>
      <!-- 主要内容 -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'AuthManager',
  data() {
    return {
      hasAuthKey: false,
      hardwareId: '',
      isInitializing: false,
      initMessage: '',
      initMessageType: '',
      authStatus: null,
      healthCheckInterval: null
    }
  },
  computed: {
    initMessageClass() {
      return {
        'message': true,
        'success': this.initMessageType === 'success',
        'error': this.initMessageType === 'error'
      }
    },
    authStatusClass() {
      if (!this.authStatus) return 'status-unknown';
      return this.authStatus.isAuthorized ? 'status-healthy' : 'status-error';
    },
    authStatusText() {
      if (!this.authStatus) return '检查中...';
      return this.authStatus.authStatus || '未知';
    }
  },
  async mounted() {
    await this.checkAuthKey();
    if (this.hasAuthKey) {
      this.startHealthMonitoring();
    }
  },
  beforeUnmount() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
  },
  methods: {
    async checkAuthKey() {
      try {
        const response = await fetch('/dongleLicense/hasAuthKey');
        const result = await response.json();
        this.hasAuthKey = result.hasAuthKey;
      } catch (error) {
        console.error('检查授权密钥失败:', error);
      }
    },
    
    async initializeAuth() {
      if (!this.hardwareId.trim()) {
        this.initMessage = '请输入硬件ID';
        this.initMessageType = 'error';
        return;
      }

      this.isInitializing = true;
      this.initMessage = '';

      try {
        const response = await fetch('/dongleLicense/initializeAuth', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            hardwareId: this.hardwareId.trim()
          })
        });
        const result = await response.json();

        if (result.success) {
          this.initMessage = '授权初始化成功！';
          this.initMessageType = 'success';
          setTimeout(() => {
            this.hasAuthKey = true;
            this.startHealthMonitoring();
          }, 2000);
        } else {
          this.initMessage = result.message;
          this.initMessageType = 'error';
        }
      } catch (error) {
        this.initMessage = '初始化失败，请检查网络连接';
        this.initMessageType = 'error';
      } finally {
        this.isInitializing = false;
      }
    },
    
    startHealthMonitoring() {
      this.healthCheckInterval = setInterval(async () => {
        try {
          const response = await fetch('/dongleLicense/authStatus');
          const result = await response.json();
          this.authStatus = result.lastAuthCheck;
        } catch (error) {
          console.error('健康检查失败:', error);
        }
      }, 30000);
    }
  }
}
</script>
```

这些示例展示了如何在前端应用中集成授权验证功能，包括状态检查、初始化流程和实时监控。
