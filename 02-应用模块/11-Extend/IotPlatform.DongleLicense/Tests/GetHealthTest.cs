using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using IotPlatform.DongleLicense.Services;
using System;
using System.Threading.Tasks;

namespace IotPlatform.DongleLicense.Tests;

/// <summary>
/// GetHealth方法测试类
/// 用于验证授权密钥文件和加密狗硬件ID对比功能
/// </summary>
public class GetHealthTest
{
    /// <summary>
    /// 测试GetHealth方法的基本功能
    /// </summary>
    public static async Task TestGetHealthMethod()
    {
        Console.WriteLine("=== GetHealth方法测试 ===");
        Console.WriteLine();

        try
        {
            // 创建服务容器
            var services = new ServiceCollection();
            
            // 添加日志服务
            services.AddLogging(builder => builder.AddConsole());
            
            // 添加加密狗相关服务
            services.AddTransient<DongleApiService>();
            services.AddTransient<DongleAuthService>();
            services.AddTransient<DongleCheckService>();
            services.AddTransient<DongleLicenseManager>();
            services.AddTransient<DongleTriggerService>();
            services.AddTransient<DongleLicenseService>();
            
            // 构建服务提供者
            var serviceProvider = services.BuildServiceProvider();
            
            // 获取DongleLicenseService实例
            var dongleLicenseService = serviceProvider.GetRequiredService<DongleLicenseService>();
            
            Console.WriteLine("1. 开始测试GetHealth方法...");
            
            // 调用GetHealth方法
            var healthResult = await dongleLicenseService.GetHealth();
            
            Console.WriteLine($"2. GetHealth方法返回结果: {healthResult}");
            
            if (healthResult)
            {
                Console.WriteLine("✅ 授权验证通过！授权密钥文件内容与加密狗硬件ID（MD5加密后）一致。");
            }
            else
            {
                Console.WriteLine("❌ 授权验证失败！可能的原因：");
                Console.WriteLine("   - 未找到授权密钥文件");
                Console.WriteLine("   - 无法获取加密狗硬件ID");
                Console.WriteLine("   - 授权密钥与当前硬件ID不匹配");
                Console.WriteLine("   - 系统发生异常");
            }
            
            Console.WriteLine();
            Console.WriteLine("3. 测试完成。");
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试过程中发生异常: {ex.Message}");
            Console.WriteLine($"详细信息: {ex}");
        }
    }
    
    /// <summary>
    /// 主测试入口
    /// </summary>
    public static async Task Main(string[] args)
    {
        Console.WriteLine("GetHealth方法功能测试");
        Console.WriteLine("功能说明：读取授权密钥文件内容和加密狗硬件读取出来的id（md5加密后）对比，一致返回true，否则返回false");
        Console.WriteLine("重要更新：DongleInfos已改为DongleInfo（单个设备），系统中始终只有一个加密狗设备");
        Console.WriteLine();

        await TestGetHealthMethod();

        Console.WriteLine();
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}
