# InitializeAuthorization 方法修改说明

## 修改概述

根据需求，对 `InitializeAuthorization` 方法进行了以下修改：

1. **改为用户传入数据**：不再自动读取加密狗信息，而是接受用户传入的硬件ID
2. **隐蔽存储路径**：将授权文件存储路径改为系统隐蔽路径，不可配置

## 具体修改内容

### 1. DongleAuthService.cs 修改

**文件路径**：`02-应用模块/11-Extend/IotPlatform.DongleLicense/Services/DongleAuthService.cs`

**修改内容**：
- 构造函数中的存储路径从可配置改为硬编码的隐蔽路径
- 新路径：`%ProgramData%\Microsoft\Windows\SystemData\Cache\sys.cache`
- 文件名伪装成系统缓存文件，增强隐蔽性

```csharp
// 修改前
var authDirectory = configuration["DongleAuth:StoragePath"] ?? 
                   Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
_authFilePath = Path.Combine(authDirectory, "dongle.auth");

// 修改后
var systemPath = Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
var authDirectory = Path.Combine(systemPath, "Microsoft", "Windows", "SystemData", "Cache");
_authFilePath = Path.Combine(authDirectory, "sys.cache");
```

### 2. DongleLicenseService.cs 修改

**文件路径**：`02-应用模块/11-Extend/IotPlatform.DongleLicense/Services/DongleLicenseService.cs`

**修改内容**：
- `InitializeAuthorization` 方法签名修改，添加输入参数
- 移除自动读取加密狗信息的逻辑
- 改为接受用户传入的硬件ID

```csharp
// 修改前
[HttpPost("/dongleLicense/initializeAuth")]
public async Task<dynamic> InitializeAuthorization()

// 修改后
[HttpPost("/dongleLicense/initializeAuth")]
public async Task<dynamic> InitializeAuthorization([FromBody] InitializeAuthInput input)
```

### 3. 新增输入模型

**文件路径**：`02-应用模块/11-Extend/IotPlatform.DongleLicense/Models/DongleLicenseDto.cs`

**新增内容**：
```csharp
/// <summary>
/// 初始化授权输入模型
/// </summary>
public class InitializeAuthInput
{
    /// <summary>
    /// 硬件ID（用户传入的授权数据）
    /// </summary>
    [Required(ErrorMessage = "硬件ID不能为空")]
    [StringLength(100, ErrorMessage = "硬件ID长度不能超过100个字符")]
    public string HardwareId { get; set; } = string.Empty;
}
```

## API 接口变化

### 请求方式

**修改前**：
```http
POST /dongleLicense/initializeAuth
Content-Type: application/json
```

**修改后**：
```http
POST /dongleLicense/initializeAuth
Content-Type: application/json

{
  "hardwareId": "01 02 03 04 05 06 07 08"
}
```

### 响应格式

响应格式保持不变，但不再返回自动读取的硬件ID，而是返回用户传入的硬件ID。

## 安全性增强

1. **隐蔽存储路径**：
   - 使用系统标准路径：`%ProgramData%\Microsoft\Windows\SystemData\Cache\`
   - 文件名伪装：`sys.cache`（看起来像系统缓存文件）
   - 路径不可配置，增强安全性

2. **用户数据控制**：
   - 支持用户传入自定义硬件ID
   - 增加输入验证和错误处理
   - 保持MD5加密存储机制

## 文档更新

同时更新了以下文档：
- `README_授权验证.md`：更新API说明和使用流程
- `前端使用示例.md`：更新前端调用示例，包括HTML和Vue.js示例

## 兼容性说明

- **向后兼容性**：此修改会破坏现有的API调用，需要更新前端代码
- **配置兼容性**：不再使用 `DongleAuth:StoragePath` 配置项
- **文件位置**：现有的授权文件需要手动迁移到新路径（如果需要保留）

## 测试建议

1. 测试新的API接口调用
2. 验证授权文件是否正确存储在新路径
3. 测试输入验证和错误处理
4. 确认授权验证功能正常工作
